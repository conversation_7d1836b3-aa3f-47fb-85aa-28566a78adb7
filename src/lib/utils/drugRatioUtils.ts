/**
 * Utility functions for drug ratio calculations and validation
 */

export interface DrugWithShare {
  share?: number | null;
  research_drug?: string;
  drug_name?: string;
}

/**
 * Validates a drug share value
 * @param share - The share value to validate
 * @returns Error message if invalid, empty string if valid
 */
export function validateDrugShare(share: number | null | undefined): string {
  if (share === null || share === undefined) return '';
  
  if (!Number.isInteger(share)) {
    return '份额必须是整数';
  }
  
  if (share < 1 || share > 9) {
    return '份额必须在1-9之间';
  }
  
  return '';
}

/**
 * Calculates ratio display string from an array of drugs with shares
 * @param drugs - Array of drugs with share values
 * @returns Ratio string (e.g., "1:2:1") or empty string if no valid shares
 */
export function calculateDrugRatio(drugs: DrugWithShare[]): string {
  const shares = drugs
    .map(drug => drug.share || 0)
    .filter(share => share > 0);
  
  if (shares.length === 0) return '';
  if (shares.length === 1) return `${shares[0]}`;
  
  return shares.join(':');
}

/**
 * Validates all drug shares in an array
 * @param drugs - Array of drugs to validate
 * @returns Array of validation errors with drug names
 */
export function validateAllDrugShares(drugs: DrugWithShare[]): string[] {
  const errors: string[] = [];
  
  drugs.forEach((drug, index) => {
    const error = validateDrugShare(drug.share);
    if (error) {
      const drugName = drug.research_drug || drug.drug_name || `药物 ${index + 1}`;
      errors.push(`${drugName}: ${error}`);
    }
  });
  
  return errors;
}

/**
 * Gets the greatest common divisor of two numbers
 * @param a - First number
 * @param b - Second number
 * @returns GCD of a and b
 */
function gcd(a: number, b: number): number {
  return b === 0 ? a : gcd(b, a % b);
}

/**
 * Simplifies a ratio by dividing all values by their GCD
 * @param shares - Array of share values
 * @returns Simplified ratio array
 */
export function simplifyRatio(shares: number[]): number[] {
  if (shares.length === 0) return [];
  if (shares.length === 1) return shares;
  
  // Find GCD of all shares
  let result = shares[0];
  for (let i = 1; i < shares.length; i++) {
    result = gcd(result, shares[i]);
  }
  
  // Divide all shares by GCD
  return shares.map(share => share / result);
}

/**
 * Gets a simplified ratio display string
 * @param drugs - Array of drugs with share values
 * @returns Simplified ratio string (e.g., "2:4:2" becomes "1:2:1")
 */
export function getSimplifiedRatioDisplay(drugs: DrugWithShare[]): string {
  const shares = drugs
    .map(drug => drug.share || 0)
    .filter(share => share > 0);
  
  if (shares.length === 0) return '';
  
  const simplified = simplifyRatio(shares);
  return simplified.join(':');
}

/**
 * Calculates percentage distribution from shares
 * @param drugs - Array of drugs with share values
 * @returns Array of objects with drug info and percentage
 */
export function calculatePercentageDistribution(drugs: DrugWithShare[]): Array<{
  drug: DrugWithShare;
  percentage: number;
}> {
  const validDrugs = drugs.filter(drug => drug.share && drug.share > 0);
  const totalShares = validDrugs.reduce((sum, drug) => sum + (drug.share || 0), 0);
  
  if (totalShares === 0) return [];
  
  return validDrugs.map(drug => ({
    drug,
    percentage: Math.round(((drug.share || 0) / totalShares) * 100 * 100) / 100 // Round to 2 decimal places
  }));
}

/**
 * Generates share options for dropdown (1-9)
 * @returns Array of share option objects
 */
export function getShareOptions(): Array<{ value: number; label: string }> {
  return Array.from({ length: 9 }, (_, i) => ({
    value: i + 1,
    label: `${i + 1}`
  }));
}
