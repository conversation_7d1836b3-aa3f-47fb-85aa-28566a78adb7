/**
 * 项目数据处理工具函数
 */

/**
 * 确保数值字段为数字类型
 * 将空字符串转换为null，将字符串数字转换为数字
 */
export function sanitizeNumericFields(project: any): any {
  if (!project) return project;

  const result = { ...project };

  // 处理数值字段
  const numericFields = [
    'disease_item_id',
    'project_stage_item_id',
    'project_status_item_id',
    'recruitment_status_item_id',
    'contract_case_total',
    'visit_count'
  ];

  for (const field of numericFields) {
    if (result[field] === '') {
      result[field] = null;
    } else if (typeof result[field] === 'string' && !isNaN(Number(result[field]))) {
      result[field] = Number(result[field]);
    }
  }

  return result;
}

/**
 * 确保项目相关数据中的数值字段为数字类型
 */
export function sanitizeProjectData(projectDetails: any): any {
  if (!projectDetails) return projectDetails;

  const result = { ...projectDetails };

  // 处理项目基本信息
  if (result.project) {
    result.project = sanitizeNumericFields(result.project);
  }

  // 处理申办方
  if (result.sponsors && Array.isArray(result.sponsors)) {
    result.sponsors = result.sponsors.map((sponsor: any) => {
      const sanitizedSponsor = { ...sponsor };
      if (sanitizedSponsor.sponsor_item_id === '') {
        sanitizedSponsor.sponsor_item_id = null;
      } else if (typeof sanitizedSponsor.sponsor_item_id === 'string' && !isNaN(Number(sanitizedSponsor.sponsor_item_id))) {
        sanitizedSponsor.sponsor_item_id = Number(sanitizedSponsor.sponsor_item_id);
      }
      return sanitizedSponsor;
    });
  }

  // 处理药物分组
  if (result.drug_groups && Array.isArray(result.drug_groups)) {
    result.drug_groups = result.drug_groups.map((group: any) => {
      const sanitizedGroup = { ...group };
      if (sanitizedGroup.share === '') {
        sanitizedGroup.share = 0;
      } else if (typeof sanitizedGroup.share === 'string' && !isNaN(Number(sanitizedGroup.share))) {
        sanitizedGroup.share = Number(sanitizedGroup.share);
      }
      return sanitizedGroup;
    });
  }

  // 处理人员角色
  if (result.personnel && Array.isArray(result.personnel)) {
    result.personnel = result.personnel.map((person: any) => {
      const sanitizedPerson = { ...person };
      if (sanitizedPerson.personnel_id === '') {
        sanitizedPerson.personnel_id = null;
      } else if (typeof sanitizedPerson.personnel_id === 'string' && !isNaN(Number(sanitizedPerson.personnel_id))) {
        sanitizedPerson.personnel_id = Number(sanitizedPerson.personnel_id);
      }

      if (sanitizedPerson.role_item_id === '') {
        sanitizedPerson.role_item_id = null;
      } else if (typeof sanitizedPerson.role_item_id === 'string' && !isNaN(Number(sanitizedPerson.role_item_id))) {
        sanitizedPerson.role_item_id = Number(sanitizedPerson.role_item_id);
      }

      return sanitizedPerson;
    });
  }

  // 处理补贴方案
  if (result.subsidy_schemes && Array.isArray(result.subsidy_schemes)) {
    result.subsidy_schemes = result.subsidy_schemes.map((scheme: any) => {
      const sanitizedScheme = { ...scheme };

      // 处理总金额
      if (sanitizedScheme.total_amount === '') {
        sanitizedScheme.total_amount = 0;
      } else if (typeof sanitizedScheme.total_amount === 'string' && !isNaN(Number(sanitizedScheme.total_amount))) {
        sanitizedScheme.total_amount = Number(sanitizedScheme.total_amount);
      }

      // 处理包含的补贴项ID数组
      if (sanitizedScheme.included_subsidies && Array.isArray(sanitizedScheme.included_subsidies)) {
        sanitizedScheme.included_subsidies = sanitizedScheme.included_subsidies.map((id: any) => {
          if (typeof id === 'string' && !isNaN(Number(id))) {
            return Number(id);
          }
          return id;
        });
      }

      return sanitizedScheme;
    });
  }

  // 处理补贴项
  if (result.subsidies && Array.isArray(result.subsidies)) {
    result.subsidies = result.subsidies.map((subsidy: any) => {
      const sanitizedSubsidy = { ...subsidy };

      if (sanitizedSubsidy.subsidy_type_item_id === '') {
        sanitizedSubsidy.subsidy_type_item_id = null;
      } else if (typeof sanitizedSubsidy.subsidy_type_item_id === 'string' && !isNaN(Number(sanitizedSubsidy.subsidy_type_item_id))) {
        sanitizedSubsidy.subsidy_type_item_id = Number(sanitizedSubsidy.subsidy_type_item_id);
      }

      if (sanitizedSubsidy.unit_amount === '') {
        sanitizedSubsidy.unit_amount = 0;
      } else if (typeof sanitizedSubsidy.unit_amount === 'string' && !isNaN(Number(sanitizedSubsidy.unit_amount))) {
        sanitizedSubsidy.unit_amount = Number(sanitizedSubsidy.unit_amount);
      }

      if (sanitizedSubsidy.total_units === '') {
        sanitizedSubsidy.total_units = 0;
      } else if (typeof sanitizedSubsidy.total_units === 'string' && !isNaN(Number(sanitizedSubsidy.total_units))) {
        sanitizedSubsidy.total_units = Number(sanitizedSubsidy.total_units);
      }

      if (sanitizedSubsidy.unit_item_id === '') {
        sanitizedSubsidy.unit_item_id = null;
      } else if (typeof sanitizedSubsidy.unit_item_id === 'string' && !isNaN(Number(sanitizedSubsidy.unit_item_id))) {
        sanitizedSubsidy.unit_item_id = Number(sanitizedSubsidy.unit_item_id);
      }

      if (sanitizedSubsidy.total_amount === '') {
        sanitizedSubsidy.total_amount = 0;
      } else if (typeof sanitizedSubsidy.total_amount === 'string' && !isNaN(Number(sanitizedSubsidy.total_amount))) {
        sanitizedSubsidy.total_amount = Number(sanitizedSubsidy.total_amount);
      }

      return sanitizedSubsidy;
    });
  }

  return result;
}
