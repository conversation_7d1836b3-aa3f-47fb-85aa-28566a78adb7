<script lang="ts">
  import { cn } from "$lib/utils";
  import { X, Edit, ChevronUp, ChevronDown } from 'lucide-svelte';
  import type { QuickFilterTag } from '$lib/types/filterConfig';

  // Props
  export let tag: QuickFilterTag;
  export let showActions = false;
  export let onEdit: ((tag: QuickFilterTag) => void) | null = null;
  export let onDelete: ((tag: QuickFilterTag) => void) | null = null;
  export let onMoveUp: ((tag: QuickFilterTag) => void) | null = null;
  export let onMoveDown: ((tag: QuickFilterTag) => void) | null = null;
  export let canMoveUp: boolean = true;
  export let canMoveDown: boolean = true;

  // 获取颜色样式
  function getColorClasses(color: string = 'blue') {
    const colorMap: Record<string, {
      bg: string,
      activeBg: string,
      text: string,
      activeText: string,
      border: string,
      activeBorder: string
    }> = {
      blue: {
        bg: "bg-blue-50",
        activeBg: "bg-blue-500",
        text: "text-blue-700",
        activeText: "text-white",
        border: "border-blue-200",
        activeBorder: "border-blue-500"
      },
      green: {
        bg: "bg-green-50",
        activeBg: "bg-green-500",
        text: "text-green-700",
        activeText: "text-white",
        border: "border-green-200",
        activeBorder: "border-green-500"
      },
      purple: {
        bg: "bg-purple-50",
        activeBg: "bg-purple-500",
        text: "text-purple-700",
        activeText: "text-white",
        border: "border-purple-200",
        activeBorder: "border-purple-500"
      },
      orange: {
        bg: "bg-orange-50",
        activeBg: "bg-orange-500",
        text: "text-orange-700",
        activeText: "text-white",
        border: "border-orange-200",
        activeBorder: "border-orange-500"
      },
      gray: {
        bg: "bg-gray-50",
        activeBg: "bg-gray-500",
        text: "text-gray-700",
        activeText: "text-white",
        border: "border-gray-200",
        activeBorder: "border-gray-500"
      }
    };

    return colorMap[color] || colorMap.blue;
  }

  const colorClasses = getColorClasses(tag.color);

  // 处理点击
  function handleClick() {
    if (tag.onClick) {
      tag.onClick();
    }
  }

  // 处理编辑
  function handleEdit(event: MouseEvent) {
    event.stopPropagation();
    if (onEdit) {
      onEdit(tag);
    }
  }

  // 处理删除
  function handleDelete(event: MouseEvent) {
    event.stopPropagation();
    if (onDelete) {
      onDelete(tag);
    }
  }

  // 处理向上移动
  function handleMoveUp(event: MouseEvent) {
    event.stopPropagation();
    if (onMoveUp) {
      onMoveUp(tag);
    }
  }

  // 处理向下移动
  function handleMoveDown(event: MouseEvent) {
    event.stopPropagation();
    if (onMoveDown) {
      onMoveDown(tag);
    }
  }
</script>

<div
  class="relative"
  role="listitem"
>
  <button
    class={cn(
      "inline-flex items-center justify-center px-1 py-0.5 rounded border transition-all duration-200 select-none",
      "hover:shadow focus:outline-none focus:ring-1 focus:ring-offset-1",
      tag.isActive
        ? `${colorClasses.activeBg} ${colorClasses.activeText} ${colorClasses.activeBorder} shadow`
        : `${colorClasses.bg} ${colorClasses.text} ${colorClasses.border} hover:${colorClasses.activeBg} hover:${colorClasses.activeText}`,
      "min-h-[18px] text-[10px] font-medium leading-none",
      showActions ? "cursor-default" : "cursor-pointer"
    )}
    onclick={showActions ? undefined : handleClick}
    data-id={tag.id}
    aria-label={`应用筛选: ${tag.label}`}
  >
    <div class="flex items-center gap-0.5">
      <span class="leading-none">{tag.label}</span>
      {#if tag.count !== undefined}
        <span class={cn(
          "text-[8px] px-0.5 py-0.5 rounded leading-none",
          tag.isActive
            ? "bg-white bg-opacity-20"
            : "bg-gray-100"
        )}>
          {tag.count}
        </span>
      {/if}
    </div>
  </button>

  <!-- 操作按钮（由父组件控制是否显示） -->
  {#if showActions}
    <div class="absolute -top-1 -right-1 flex gap-0.5">
      <!-- 向上移动按钮 -->
      {#if onMoveUp && canMoveUp}
        <button
          onclick={handleMoveUp}
          class="w-3 h-3 bg-gray-500 text-white rounded-full flex items-center justify-center hover:bg-gray-600 transition-colors shadow-sm"
          aria-label="向上移动"
          title="向上移动"
        >
          <ChevronUp class="h-1.5 w-1.5" />
        </button>
      {/if}

      <!-- 向下移动按钮 -->
      {#if onMoveDown && canMoveDown}
        <button
          onclick={handleMoveDown}
          class="w-3 h-3 bg-gray-500 text-white rounded-full flex items-center justify-center hover:bg-gray-600 transition-colors shadow-sm"
          aria-label="向下移动"
          title="向下移动"
        >
          <ChevronDown class="h-1.5 w-1.5" />
        </button>
      {/if}

      <!-- 编辑按钮 -->
      {#if onEdit}
        <button
          onclick={handleEdit}
          class="w-3 h-3 bg-blue-500 text-white rounded-full flex items-center justify-center hover:bg-blue-600 transition-colors shadow-sm"
          aria-label="编辑筛选配置"
          title="编辑"
        >
          <Edit class="h-1.5 w-1.5" />
        </button>
      {/if}

      <!-- 删除按钮 -->
      {#if onDelete}
        <button
          onclick={handleDelete}
          class="w-3 h-3 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors shadow-sm"
          aria-label="删除筛选配置"
          title="删除"
        >
          <X class="h-1.5 w-1.5" />
        </button>
      {/if}
    </div>
  {/if}
</div>
