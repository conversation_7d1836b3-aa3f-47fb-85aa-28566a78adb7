<script lang="ts">
  export let title = '';
  export let count: number | string = 0;
  export let color: 'blue' | 'green' | 'purple' | 'orange' | 'gray' = 'blue';
  export let selectable = false;
  export let active = false;
  export let onClick: (() => void) | null = null;
  export let size: 'sm' | 'md' = 'sm';

  function colorClasses(c: string) {
    const map: Record<string, { bg: string; text: string; border: string; hover: string; activeBg: string }> = {
      blue: {
        bg: 'bg-blue-50',
        text: 'text-blue-700',
        border: 'border-blue-200',
        hover: 'hover:bg-blue-100',
        activeBg: 'bg-blue-100'
      },
      green: {
        bg: 'bg-green-50',
        text: 'text-green-700',
        border: 'border-green-200',
        hover: 'hover:bg-green-100',
        activeBg: 'bg-green-100'
      },
      purple: {
        bg: 'bg-purple-50',
        text: 'text-purple-700',
        border: 'border-purple-200',
        hover: 'hover:bg-purple-100',
        activeBg: 'bg-purple-100'
      },
      orange: {
        bg: 'bg-orange-50',
        text: 'text-orange-700',
        border: 'border-orange-200',
        hover: 'hover:bg-orange-100',
        activeBg: 'bg-orange-100'
      },
      gray: {
        bg: 'bg-gray-50',
        text: 'text-gray-700',
        border: 'border-gray-200',
        hover: 'hover:bg-gray-100',
        activeBg: 'bg-gray-100'
      }
    };
    return map[c] || map.blue;
  }

  function handleClick() {
    if (onClick) {
      onClick();
    }
  }

  function getSizeClasses() {
    return size === 'md'
      ? {
          container: 'px-3 py-1.5 gap-1.5',
          title: 'text-sm',
          count: 'text-sm px-1.5 py-0.5'
        }
      : {
          container: 'px-1.5 py-0.5 gap-0.5',
          title: 'text-xs',
          count: 'text-xs'
        };
  }
</script>

{#if selectable}
  <button
    type="button"
    class={`inline-flex items-center ${getSizeClasses().container} rounded-md border transition-all duration-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-1 ${colorClasses(color).border} ${colorClasses(color).text} ${colorClasses(color).hover} ${active ? `${colorClasses(color).activeBg} ring-2 ring-offset-0 shadow-sm` : 'bg-white hover:shadow-sm'}`}
    on:click={handleClick}
    aria-pressed={active}
  >
    <span class={`${getSizeClasses().title} font-medium leading-none`}>{title}</span>
    <span class={`${getSizeClasses().count} font-semibold tabular-nums leading-none rounded ${active ? 'bg-white/20' : colorClasses(color).bg}`}>{count}</span>
  </button>
{:else}
  <div
    class={`inline-flex items-center ${getSizeClasses().container} rounded-md border ${colorClasses(color).bg} ${colorClasses(color).text} ${colorClasses(color).border}`}
    aria-hidden="true"
  >
    <span class={`${getSizeClasses().title} leading-none`}>{title}</span>
    <span class={`${getSizeClasses().count} font-semibold tabular-nums leading-none rounded ${colorClasses(color).bg}`}>{count}</span>
  </div>
{/if}
