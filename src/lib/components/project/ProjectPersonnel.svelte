<script lang="ts">
  import { onMount } from 'svelte';
  import { Button } from '$lib/components/ui/button';
  import Input from '$lib/components/ui/input.svelte';
  import { sqliteDictionaryService } from '$lib/services/sqliteDictionaryService';
  import type { ProjectWithDetails, ProjectPersonnelWithDetails } from '$lib/services/projectManagementService';
  import { staffService } from '$lib/services/staffService';

  // 组件属性
  const { projectDetails } = $props();

  // 状态管理
  let personnel = $state(projectDetails.personnel || []);
  let dialogOpen = $state(false);
  let batchDialogOpen = $state(false);
  let selectedPersonnelId = $state<number | null>(null);
  let selectedRoleId = $state<string>(''); // 使用字符串类型，避免类型转换问题
  let searchName = $state('');
  let searchResults = $state<{id: number, name: string}[]>([]);
  let isSearching = $state(false);
  let debugInfo = $state('');

  // 批量添加状态
  let batchPersonnel = $state<{id: number, name: string, roles: number[]}[]>([]);
  let batchSearchName = $state('');
  let batchSearchResults = $state<{id: number, name: string}[]>([]);
  let isBatchSearching = $state(false);
  let selectedRoleIds = $state<number[]>([]);
  let currentPersonnelIndex = $state<number | null>(null);

  // 字典项
  let roleItems = $state<{item_id: number | undefined, item_value: string}[]>([]);

  // 加载角色字典项
  async function loadRoleItems() {
    try {
      const rolesDict = await sqliteDictionaryService.getDictByName('研究角色');
      if (rolesDict && rolesDict.items) {
        roleItems = rolesDict.items.map(item => ({
          item_id: item.item_id,
          item_value: item.value
        }));
        console.log('加载角色字典项成功:', roleItems);
      } else {
        console.error('未找到研究角色字典');
      }
    } catch (err) {
      console.error('加载角色字典项失败:', err);
    }
  }

  // 搜索人员
  async function searchPersonnel() {
    if (!searchName) return;

    isSearching = true;
    debugInfo = '正在搜索人员...';

    try {
      const data = await staffService.queryStaff({ name: searchName });
      searchResults = data.map(item => ({
        id: item.id || 0,
        name: item.name
      }));
      debugInfo = `搜索到 ${searchResults.length} 个结果`;
      console.log('搜索人员结果:', searchResults);
    } catch (err) {
      console.error('搜索人员失败:', err);
      debugInfo = '搜索人员失败';
    } finally {
      isSearching = false;
    }
  }

  // 批量搜索人员
  async function batchSearchPersonnel() {
    if (!batchSearchName) return;

    isBatchSearching = true;

    try {
      const data = await staffService.queryStaff({ name: batchSearchName });
      batchSearchResults = data.map(item => ({
        id: item.id || 0,
        name: item.name
      }));
      console.log('批量搜索人员结果:', batchSearchResults);
    } catch (err) {
      console.error('批量搜索人员失败:', err);
    } finally {
      isBatchSearching = false;
    }
  }

  // 选择人员
  function selectPersonnel(id: number, name: string) {
    console.log('选择人员:', { id, name });
    selectedPersonnelId = id;
    searchName = name;
    searchResults = [];
    debugInfo = `已选择人员: ${name} (ID: ${id})`;
  }

  // 批量选择人员
  function selectBatchPersonnel(id: number, name: string) {
    console.log('批量选择人员:', { id, name });

    // 检查是否已经添加过该人员
    const existingIndex = batchPersonnel.findIndex(p => p.id === id);

    if (existingIndex === -1) {
      // 添加新人员
      batchPersonnel = [...batchPersonnel, { id, name, roles: [] }];
      currentPersonnelIndex = batchPersonnel.length - 1;
    } else {
      // 选择已有人员
      currentPersonnelIndex = existingIndex;
    }

    // 更新选中的角色
    selectedRoleIds = batchPersonnel[currentPersonnelIndex].roles;

    batchSearchName = '';
    batchSearchResults = [];
  }

  // 切换角色选择
  function toggleRoleSelection(roleId: number | undefined) {
    if (currentPersonnelIndex === null || roleId === undefined) return;

    const index = selectedRoleIds.indexOf(roleId);

    if (index === -1) {
      // 添加角色
      selectedRoleIds = [...selectedRoleIds, roleId];
    } else {
      // 移除角色
      selectedRoleIds = selectedRoleIds.filter(id => id !== roleId);
    }

    // 更新批量人员数据
    batchPersonnel[currentPersonnelIndex].roles = selectedRoleIds;
  }

  // 移除批量人员
  function removeBatchPersonnel(index: number) {
    batchPersonnel = batchPersonnel.filter((_, i) => i !== index);

    if (currentPersonnelIndex === index) {
      currentPersonnelIndex = null;
      selectedRoleIds = [];
    } else if (currentPersonnelIndex !== null && currentPersonnelIndex > index) {
      currentPersonnelIndex--;
    }
  }

  // 添加人员角色
  function addPersonnel() {
    console.log('添加人员按钮点击:', {
      selectedPersonnelId,
      selectedRoleId,
      selectedRoleIdType: typeof selectedRoleId
    });

    if (!selectedPersonnelId) {
      alert('请先选择人员');
      return;
    }

    if (!selectedRoleId) {
      alert('请选择角色');
      return;
    }

    // 移除项目ID检查，允许在新建项目时添加人员
    // 项目ID将在保存项目时由后端分配

    // 将角色ID转换为数字
    const roleId = parseInt(selectedRoleId);
    if (isNaN(roleId)) {
      alert('角色ID无效');
      return;
    }

    // 检查是否已存在相同的人员和角色
    const exists = personnel.some((p: any) =>
      p.personnel_id === selectedPersonnelId && p.role_item_id === roleId
    );

    if (exists) {
      alert('该人员已分配相同角色');
      return;
    }

    // 获取角色详情
    const roleItem = roleItems.find((item) => item.item_id === roleId);
    if (!roleItem) {
      alert('未找到角色信息');
      return;
    }

    console.log('添加研究人员:', {
      personnelId: selectedPersonnelId,
      name: searchName,
      roleId: roleId,
      roleName: roleItem.item_value
    });

    // 创建新人员角色
    // 如果项目ID不存在，使用空字符串，后端会在保存项目时分配正确的ID
    const newPersonnel = {
      project_id: projectDetails.project.project_id || '',
      personnel_id: selectedPersonnelId,
      role_item_id: roleId,
      personnel: {
        id: selectedPersonnelId,
        name: searchName
      },
      role: {
        item_id: roleItem.item_id || 0,
        dictionary_id: 0,
        item_key: '',
        item_value: roleItem.item_value
      }
    };

    // 添加到列表
    personnel = [...personnel, newPersonnel];

    // 更新 projectDetails
    if (Array.isArray(projectDetails.personnel)) {
      projectDetails.personnel.push(newPersonnel);
    } else {
      projectDetails.personnel = personnel;
    }

    console.log('更新后的研究人员列表:', personnel);
    debugInfo = `已添加人员: ${searchName}, 角色: ${roleItem.item_value}`;

    // 重置选择并关闭对话框
    selectedPersonnelId = null;
    selectedRoleId = '';
    searchName = '';
    dialogOpen = false;
  }

  // 批量添加人员角色
  function addBatchPersonnel() {
    if (batchPersonnel.length === 0) {
      alert('请先添加人员');
      return;
    }

    // 检查每个人员是否都有至少一个角色
    const invalidPersonnel = batchPersonnel.find(p => p.roles.length === 0);
    if (invalidPersonnel) {
      alert(`人员 "${invalidPersonnel.name}" 没有分配角色，请为每个人员至少分配一个角色`);
      return;
    }

    // 创建新的人员角色记录
    let newPersonnelList: any[] = [];

    batchPersonnel.forEach(person => {
      person.roles.forEach(roleId => {
        // 检查是否已存在相同的人员和角色
        const exists = personnel.some((p: any) =>
          p.personnel_id === person.id && p.role_item_id === roleId
        );

        if (!exists) {
          // 获取角色详情
          const roleItem = roleItems.find((item) => item.item_id === roleId);
          if (roleItem) {
            const newPersonnel = {
              project_id: projectDetails.project.project_id || '',
              personnel_id: person.id,
              role_item_id: roleId,
              personnel: {
                id: person.id,
                name: person.name
              },
              role: {
                item_id: roleItem.item_id || 0,
                dictionary_id: 0,
                item_key: '',
                item_value: roleItem.item_value
              }
            };
            newPersonnelList.push(newPersonnel);
          }
        }
      });
    });

    if (newPersonnelList.length === 0) {
      alert('所有人员角色组合已存在，没有新增记录');
      return;
    }

    // 添加到列表
    personnel = [...personnel, ...newPersonnelList];

    // 更新 projectDetails
    if (Array.isArray(projectDetails.personnel)) {
      projectDetails.personnel.push(...newPersonnelList);
    } else {
      projectDetails.personnel = personnel;
    }

    console.log('批量添加后的研究人员列表:', personnel);
    debugInfo = `已批量添加 ${newPersonnelList.length} 条人员角色记录`;

    // 重置选择并关闭对话框
    batchPersonnel = [];
    currentPersonnelIndex = null;
    selectedRoleIds = [];
    batchDialogOpen = false;
  }

  // 删除人员角色
  function removePersonnel(index: number) {
    if (!projectDetails.personnel) {
      console.error('项目人员列表不存在');
      return;
    }

    console.log('删除研究人员:', { index, person: personnel[index] });

    // 更新本地状态
    personnel = personnel.filter((_: any, i: number) => i !== index);

    // 更新 projectDetails
    projectDetails.personnel = projectDetails.personnel.filter((_: any, i: number) => i !== index);

    console.log('删除后的研究人员列表:', personnel);
    debugInfo = '已删除人员';
  }

  // 获取角色名称
  function getRoleName(roleId: number): string {
    const role = roleItems.find(r => r.item_id === roleId);
    return role ? role.item_value : '未知角色';
  }

  // 定义关键角色
  const criticalRoles = ['主要研究者', '临床协调员(CRC)', 'CRA'];

  // 获取角色特定的颜色样式
  function getRoleColorClass(roleName: string): { label: string; name: string } {
    const normalized = roleName.toLowerCase();

    if (normalized.includes('研究者') || normalized.includes('pi')) {
      return {
        label: 'text-blue-900',
        name: 'text-blue-800'
      };
    }
    if (normalized.includes('协调') || normalized.includes('crc')) {
      return {
        label: 'text-purple-900',
        name: 'text-purple-800'
      };
    }
    if (normalized.includes('监查') || normalized.includes('cra')) {
      return {
        label: 'text-amber-900',
        name: 'text-amber-800'
      };
    }
    // 默认颜色
    return {
      label: 'text-blue-900',
      name: 'text-blue-800'
    };
  }

  // 按角色分组人员
  function groupPersonnelByRole() {
    type PersonnelItem = typeof personnel[0];

    const roleGroups: {
      roleId: number;
      roleName: string;
      personnel: Array<{
        index: number;
        person: PersonnelItem;
      }>;
    }[] = [];

    // 遍历所有人员，按角色分组
    personnel.forEach((person: PersonnelItem, index: number) => {
      const roleId = person.role_item_id;
      const roleName = person.role?.item_value || '未知角色';

      // 查找该角色是否已存在于分组中
      let roleGroup = roleGroups.find(group => group.roleId === roleId);

      if (!roleGroup) {
        // 如果角色不存在，创建新的角色分组
        roleGroup = {
          roleId,
          roleName,
          personnel: []
        };
        roleGroups.push(roleGroup);
      }

      // 将人员添加到对应角色分组
      roleGroup.personnel.push({
        index,
        person
      });
    });

    // 按角色名称排序
    roleGroups.sort((a, b) => a.roleName.localeCompare(b.roleName));

    return roleGroups;
  }

  // 分离关键角色和其他角色
  function separatePersonnelByImportance() {
    const allGroups = groupPersonnelByRole();
    const criticalGroups = allGroups.filter(group => criticalRoles.includes(group.roleName));
    const otherGroups = allGroups.filter(group => !criticalRoles.includes(group.roleName));

    return { criticalGroups, otherGroups };
  }



  // 控制其他角色的展开/收起状态
  let showOtherRoles = $state(false);

  // 组件挂载时加载角色字典项
  onMount(async () => {
    await loadRoleItems();

    // 确保 personnel 状态与 projectDetails.personnel 同步
    if (projectDetails.personnel) {
      personnel = projectDetails.personnel;
      console.log('初始化研究人员列表:', personnel);
    }
  });
</script>

<div>
  <div class="flex justify-between items-center mb-6">
    <div>
      <h2 class="text-xl font-semibold">研究人员</h2>
      <p class="text-sm text-gray-500 mt-1">添加项目的研究人员及其角色</p>
    </div>

    <div class="flex gap-2">
      <Button on:click={() => dialogOpen = true} variant="outline" class="gap-2">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus"><path d="M5 12h14"/><path d="M12 5v14"/></svg>
        添加研究人员
      </Button>
      <Button on:click={() => batchDialogOpen = true} variant="outline" class="gap-2">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users-plus"><path d="M14 19a6 6 0 0 0-12 0"/><circle cx="8" cy="9" r="4"/><path d="M22 19a6 6 0 0 0-6-6 4 4 0 1 0 0-8"/><path d="M16 3h6"/><path d="M19 6v-6"/></svg>
        批量添加人员
      </Button>
    </div>

    {#if dialogOpen}
      <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white p-6 rounded-lg shadow-lg w-full max-w-md">
          <h3 class="text-lg font-semibold mb-4">添加研究人员</h3>
          <p class="text-gray-500 text-sm mb-4">搜索并选择人员，然后为其分配角色</p>

          <div class="py-4 space-y-6">
            <!-- 搜索人员 -->
            <div class="space-y-2">
              <label for="search-personnel" class="block text-sm font-medium mb-1">搜索人员</label>
              <div class="flex gap-2">
                <Input
                  id="search-personnel"
                  placeholder="输入人员姓名搜索"
                  bind:value={searchName}
                />
                <Button variant="outline" on:click={searchPersonnel} disabled={isSearching}>
                  {#if isSearching}
                    <svg class="animate-spin h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  {:else}
                    搜索
                  {/if}
                </Button>
              </div>
              <p class="text-xs text-gray-500">输入人员姓名后点击搜索按钮</p>

              {#if searchResults.length > 0}
                <div class="border rounded-md max-h-40 overflow-y-auto mt-2">
                  <ul class="divide-y">
                    {#each searchResults as result}
                      <button
                        type="button"
                        class="w-full text-left px-3 py-2 hover:bg-gray-100 cursor-pointer"
                        onclick={() => selectPersonnel(result.id, result.name)}
                      >
                        {result.name}
                      </button>
                    {/each}
                  </ul>
                </div>
              {/if}

              {#if selectedPersonnelId}
                <div class="mt-2 p-2 bg-blue-50 rounded-md text-blue-700 text-sm">
                  已选择: <span class="font-medium">{searchName}</span>
                </div>
              {/if}
            </div>

            <!-- 选择角色 -->
            <div>
              <label for="role-select" class="block text-sm font-medium mb-1">选择角色</label>
              <select
                id="role-select"
                bind:value={selectedRoleId}
                class="h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
              >
                <option value="">请选择角色</option>
                {#each roleItems as role}
                  <option value={role.item_id}>{role.item_value}</option>
                {/each}
              </select>
              <p class="text-xs text-gray-500 mt-1">为选中的人员分配项目中的角色</p>
            </div>
          </div>

          <!-- 调试信息 -->
          {#if debugInfo}
            <div class="mt-2 p-2 bg-gray-100 rounded text-xs text-gray-700">
              调试信息: {debugInfo}
            </div>
          {/if}

          <div class="flex justify-end gap-2 pt-2 border-t mt-4">
            <Button on:click={() => dialogOpen = false} variant="outline">取消</Button>
            <Button
              on:click={addPersonnel}
            >
              添加
            </Button>
          </div>
        </div>
      </div>
    {/if}

    {#if batchDialogOpen}
      <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white p-6 rounded-lg shadow-lg w-full max-w-4xl">
          <h3 class="text-lg font-semibold mb-4">批量添加研究人员</h3>
          <p class="text-gray-500 text-sm mb-4">添加多个人员并为每个人员分配多个角色</p>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- 左侧：人员列表 -->
            <div class="border rounded-lg p-4">
              <h4 class="font-medium mb-3">已选择的人员</h4>

              <!-- 搜索人员 -->
              <div class="mb-4">
                <div class="flex gap-2 mb-2">
                  <Input
                    placeholder="输入人员姓名搜索"
                    bind:value={batchSearchName}
                  />
                  <Button variant="outline" on:click={batchSearchPersonnel} disabled={isBatchSearching}>
                    {#if isBatchSearching}
                      <svg class="animate-spin h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    {:else}
                      搜索
                    {/if}
                  </Button>
                </div>

                {#if batchSearchResults.length > 0}
                  <div class="border rounded-md max-h-40 overflow-y-auto mt-2">
                    <ul class="divide-y">
                      {#each batchSearchResults as result}
                        <button
                          type="button"
                          class="w-full text-left px-3 py-2 hover:bg-gray-100 cursor-pointer"
                          onclick={() => selectBatchPersonnel(result.id, result.name)}
                        >
                          {result.name}
                        </button>
                      {/each}
                    </ul>
                  </div>
                {/if}
              </div>

              <!-- 已选择的人员列表 -->
              <div class="border rounded-md overflow-hidden">
                <table class="w-full border-collapse">
                  <thead>
                    <tr class="bg-gray-50">
                      <th class="text-left py-2 px-3 text-xs font-medium text-gray-700">人员姓名</th>
                      <th class="text-left py-2 px-3 text-xs font-medium text-gray-700">已分配角色</th>
                      <th class="w-[60px] text-center py-2 px-3 text-xs font-medium text-gray-700">操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    {#if batchPersonnel.length === 0}
                      <tr>
                        <td colspan="3" class="py-4 text-center text-gray-500 text-sm">
                          暂无已选择的人员，请搜索并添加人员
                        </td>
                      </tr>
                    {:else}
                      {#each batchPersonnel as person, index}
                        <tr class="border-t hover:bg-gray-50 {currentPersonnelIndex === index ? 'bg-blue-50' : ''}">
                          <td class="py-2 px-3 text-sm">
                            <button
                              class="text-blue-600 hover:underline text-left w-full"
                              onclick={() => {
                                currentPersonnelIndex = index;
                                selectedRoleIds = person.roles;
                              }}
                            >
                              {person.name}
                            </button>
                          </td>
                          <td class="py-2 px-3">
                            <div class="flex flex-wrap gap-1">
                              {#if person.roles.length === 0}
                                <span class="text-xs text-gray-500">未分配角色</span>
                              {:else}
                                {#each person.roles as roleId}
                                  <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    {getRoleName(roleId)}
                                  </span>
                                {/each}
                              {/if}
                            </div>
                          </td>
                          <td class="py-2 px-3 text-center">
                            <button
                              class="text-red-500 hover:text-red-700"
                              onclick={() => removeBatchPersonnel(index)}
                              aria-label="删除人员"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trash-2"><path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/><line x1="10" x2="10" y1="11" y2="17"/><line x1="14" x2="14" y1="11" y2="17"/></svg>
                            </button>
                          </td>
                        </tr>
                      {/each}
                    {/if}
                  </tbody>
                </table>
              </div>
            </div>

            <!-- 右侧：角色选择 -->
            <div class="border rounded-lg p-4">
              <h4 class="font-medium mb-3">
                {#if currentPersonnelIndex !== null}
                  为 <span class="text-blue-600">{batchPersonnel[currentPersonnelIndex].name}</span> 分配角色
                {:else}
                  请先选择左侧人员
                {/if}
              </h4>

              {#if currentPersonnelIndex !== null}
                <div class="space-y-2">
                  <p class="text-sm text-gray-500 mb-2">点击角色进行选择或取消选择</p>
                  <div class="grid grid-cols-2 gap-2">
                    {#each roleItems as role}
                      {#if role.item_id !== undefined}
                        <button
                          type="button"
                          class="text-left px-3 py-2 rounded border {selectedRoleIds.includes(role.item_id) ? 'bg-blue-100 border-blue-300' : 'bg-gray-50 hover:bg-gray-100 border-gray-200'}"
                          onclick={() => toggleRoleSelection(role.item_id)}
                        >
                          <div class="flex items-center">
                            <div class="flex-1">
                              {role.item_value}
                            </div>
                            {#if selectedRoleIds.includes(role.item_id)}
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-blue-600"><polyline points="20 6 9 17 4 12"></polyline></svg>
                            {/if}
                          </div>
                        </button>
                      {/if}
                    {/each}
                  </div>
                </div>
              {:else}
                <div class="flex items-center justify-center h-40 text-gray-500">
                  <div class="text-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="mx-auto mb-2"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg>
                    <p>请先在左侧选择人员</p>
                  </div>
                </div>
              {/if}
            </div>
          </div>

          <div class="flex justify-end gap-2 pt-2 border-t mt-6">
            <Button on:click={() => batchDialogOpen = false} variant="outline">取消</Button>
            <Button
              on:click={addBatchPersonnel}
              disabled={batchPersonnel.length === 0}
            >
              批量添加
            </Button>
          </div>
        </div>
      </div>
    {/if}




  </div>

  {#if !personnel || personnel.length === 0}
    <div class="bg-gray-50 border border-gray-200 rounded-lg p-8 text-center">
      <div class="flex justify-center mb-4">
        <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="text-gray-400"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg>
      </div>
      <h3 class="text-lg font-medium text-gray-900 mb-2">暂无研究人员数据</h3>
      <p class="text-gray-500 mb-4">@</p>
      <div class="flex gap-2 justify-center">
        <Button on:click={() => dialogOpen = true} variant="outline" class="gap-2">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus"><path d="M5 12h14"/><path d="M12 5v14"/></svg>
          添加研究人员
        </Button>
        <Button on:click={() => batchDialogOpen = true} variant="outline" class="gap-2">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users-plus"><path d="M14 19a6 6 0 0 0-12 0"/><circle cx="8" cy="9" r="4"/><path d="M22 19a6 6 0 0 0-6-6 4 4 0 1 0 0-8"/><path d="M16 3h6"/><path d="M19 6v-6"/></svg>
          批量添加人员
        </Button>
      </div>
    </div>
  {:else}
    {@const { criticalGroups, otherGroups } = separatePersonnelByImportance()}

    <div class="space-y-6">
      <!-- 关键角色简洁显示 -->
      {#if criticalGroups.length > 0 || criticalRoles.some(role => !criticalGroups.find(g => g.roleName === role))}
        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border-2 border-blue-200 rounded-lg p-4">
          <div class="flex items-center mb-3">
            <div class="bg-blue-100 p-1.5 rounded-lg mr-2">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-blue-600">
                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                <circle cx="9" cy="7" r="4"></circle>
                <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
              </svg>
            </div>
            <h3 class="text-lg font-bold text-blue-800">关键研究人员</h3>
          </div>

          <div class="space-y-2">
            {#each criticalRoles as roleName}
              {@const roleGroup = criticalGroups.find(g => g.roleName === roleName)}
              {@const colorClass = getRoleColorClass(roleName)}
              <div class="flex items-center justify-between bg-white rounded-lg p-3 border border-blue-100">
                <div class="flex-1">
                  {#if roleGroup && roleGroup.personnel.length > 0}
                    <span class="font-bold {colorClass.label}">
                      {roleName.replace('临床协调员(CRC)', 'CRC').replace('主要研究者', 'PI')}：
                    </span>
                    <span class="font-medium {colorClass.name}">
                      {roleGroup.personnel.map(p => p.person.personnel?.name || '未知人员').join('、')}
                    </span>
                  {:else}
                    <span class="font-bold {colorClass.label}">
                      {roleName.replace('临床协调员(CRC)', 'CRC').replace('主要研究者', 'PI')}：
                    </span>
                    <span class="text-gray-500 italic">未分配</span>
                  {/if}
                </div>
                {#if roleGroup && roleGroup.personnel.length > 0}
                  <div class="flex gap-1">
                    {#each roleGroup.personnel as { person, index }}
                      <Button
                        variant="ghost"
                        size="sm"
                        on:click={() => removePersonnel(index)}
                        class="text-red-500 hover:text-red-700 hover:bg-red-50 p-1"
                        title="删除 {person.personnel?.name || '未知人员'}"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/><line x1="10" x2="10" y1="11" y2="17"/><line x1="14" x2="14" y1="11" y2="17"/>
                        </svg>
                      </Button>
                    {/each}
                  </div>
                {/if}
              </div>
            {/each}
          </div>
        </div>
      {/if}

      <!-- 其他角色 -->
      {#if otherGroups.length > 0}
        <div class="bg-white border rounded-lg overflow-hidden">
          <div class="bg-gray-50 px-4 py-3 border-b flex items-center justify-between">
            <div class="flex items-center">
              <h3 class="font-medium text-gray-700">其他研究人员</h3>
              <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-600">
                {otherGroups.reduce((total, group) => total + group.personnel.length, 0)} 人
              </span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              on:click={() => showOtherRoles = !showOtherRoles}
              class="text-gray-600 hover:text-gray-800"
            >
              {showOtherRoles ? '收起' : '展开'}
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-1 transform transition-transform {showOtherRoles ? 'rotate-180' : ''}">
                <polyline points="6 9 12 15 18 9"></polyline>
              </svg>
            </Button>
          </div>

          {#if showOtherRoles}
            <table class="w-full border-collapse">
              <thead>
                <tr class="bg-gray-50">
                  <th class="text-left py-3 px-4 font-medium text-gray-700">角色</th>
                  <th class="text-left py-3 px-4 font-medium text-gray-700">人员</th>
                  <th class="w-[100px] text-right py-3 px-4 font-medium text-gray-700">操作</th>
                </tr>
              </thead>
              <tbody>
                {#each otherGroups as roleGroup}
                  <tr class="border-t hover:bg-gray-50 bg-gray-50">
                    <td class="py-3 px-4 font-medium">
                      <span class="inline-flex items-center px-2.5 py-1 rounded-md text-sm font-medium bg-gray-100 text-gray-700">
                        {roleGroup.roleName}
                      </span>
                    </td>
                    <td class="py-3 px-4">
                      <div class="flex flex-wrap gap-1">
                        {#each roleGroup.personnel as { person } }
                          <span class="inline-flex items-center px-2 py-1 rounded-md text-xs bg-gray-100 text-gray-800">
                            {person.personnel?.name || '未知人员'}
                          </span>
                        {/each}
                      </div>
                    </td>
                    <td class="text-right py-3 px-4">
                      <!-- 角色行没有删除按钮 -->
                    </td>
                  </tr>
                  <!-- 每个角色下的人员行 -->
                  {#each roleGroup.personnel as { person, index }}
                    <tr class="border-t border-gray-100 hover:bg-gray-50">
                      <td class="py-2 px-4 pl-8 text-sm text-gray-500">
                        <!-- 缩进显示 -->
                      </td>
                      <td class="py-2 px-4 text-sm">
                        {person.personnel?.name || '未知人员'}
                      </td>
                      <td class="text-right py-2 px-4">
                        <Button variant="ghost" size="sm" on:click={() => removePersonnel(index)} class="text-red-500 hover:text-red-700">
                          删除
                        </Button>
                      </td>
                    </tr>
                  {/each}
                {/each}
              </tbody>
            </table>
          {/if}
        </div>
      {/if}
    </div>
  {/if}

  <!-- 帮助提示 -->
  <div class="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
    <h3 class="text-sm font-medium text-blue-800 mb-2">研究人员信息说明</h3>
    <ul class="list-disc pl-5 text-sm text-blue-700 space-y-1">
      <li>研究人员是指参与项目的工作人员</li>
      <li>每个人员可以分配不同的角色，如主要研究者、协调员等</li>
      <li>人员列表按角色分组显示，方便查看每个角色下有哪些人员</li>
      <li>添加人员时，需要先搜索人员，然后为其分配角色</li>
      <li>批量添加功能允许同时添加多个人员，并为每个人员分配多个角色</li>
      <li>研究人员信息为可选项，您可以稍后再添加</li>
    </ul>
  </div>
</div>
