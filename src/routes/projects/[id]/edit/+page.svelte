<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { projectManagementService, type ProjectWithDetails, type SubsidyScheme } from '$lib/services/projectManagementService';
  import { Button } from '$lib/components/ui/button';
  import ProjectForm from '$lib/components/project/ProjectForm.svelte';
  import { ArrowLeft, Save } from 'lucide-svelte';
  import { page } from '$app/state';
  import { sanitizeProjectData } from '$lib/utils/projectDataUtils';

  // 获取项目ID
  const projectId = $derived(page.params.id);
  $effect(() => {
    console.log('编辑页面接收到的项目ID:', projectId);
  });

  // 状态管理
  let projectDetails = $state<ProjectWithDetails | null>(null);
  let isLoading = $state(true); // 默认为加载中状态
  let isSaving = $state(false);
  let error = $state<string | null>(null);
  let successMessage = $state<string | null>(null);
  let isDebug = $state(false); // 调试模式

  // 创建默认项目结构，以防获取失败
  function createDefaultProject(): ProjectWithDetails {
    return {
      project: {
        project_id: projectId,
        project_name: '',
        project_short_name: '',
        project_path: '',
        visit_count: undefined,
        visit_flow_chart_path: undefined,
      },
      sponsors: [],
      research_drugs: [],
      drug_groups: [],
      personnel: [],
      subsidy_schemes: [],
      subsidies: []
    };
  }

  // 加载项目详情
  async function loadProjectDetails() {
    isLoading = true;
    error = null;

    try {
      // 确保 projectId 是有效的
      if (!projectId || projectId === 'undefined' || projectId === 'null') {
        throw new Error('无效的项目ID');
      }

      console.log('正在加载项目详情，项目ID:', projectId);

      // 使用 projectManagementService 获取项目详情
      const result = await projectManagementService.getProjectDetails(projectId);

      console.log('获取到项目详情:', result);

      // 检查结果是否为null或undefined
      if (!result) {
        console.warn('未找到项目数据，创建默认项目');
        projectDetails = createDefaultProject();
      } else if (!result.project) {
        console.warn('项目数据结构不完整，补充默认结构');
        // 使用类型断言避免TypeScript错误
        const incompleteResult = result as any;
        projectDetails = {
          ...incompleteResult,
          project: {
            project_id: projectId,
            project_name: incompleteResult.project?.project_name || '',
            project_short_name: incompleteResult.project?.project_short_name || '',
            project_path: incompleteResult.project?.project_path || '',
            visit_count: incompleteResult.project?.visit_count,
            visit_flow_chart_path: incompleteResult.project?.visit_flow_chart_path,
          }
        };
      } else {
        projectDetails = result;
      }

      console.log('项目详情已设置:', projectDetails);
    } catch (err) {
      console.error('加载项目详情失败:', err);
      error = err instanceof Error ? err.message : String(err);

      // 即使加载失败，也创建一个默认的结构以便编辑
      projectDetails = createDefaultProject();
    } finally {
      isLoading = false;
    }
  }

  // 保存项目
  async function saveProject() {
    console.log("[saveProject] Function start"); // 入口日志
    if (!projectDetails) {
      console.error("[saveProject] projectDetails is null, exiting.");
      return;
    }

    // 验证必填字段
    if (!projectDetails.project.project_name) {
      error = "项目名称为必填项";
      console.warn("[saveProject] Validation failed: project_name missing.");
      return;
    }
    if (!projectDetails.project.project_short_name) {
      error = "项目简称为必填项";
      console.warn("[saveProject] Validation failed: project_short_name missing.");
      return;
    }
    if (!projectDetails.project.disease_item_id) {
      error = "疾病类型为必填项";
      console.warn("[saveProject] Validation failed: disease_item_id missing.");
      return;
    }

    console.log("[saveProject] Validation passed. Setting state...");
    isSaving = true;
    isLoading = true;
    error = null;
    successMessage = null;

    try {
      console.log("[saveProject] Entering try block.");
      // 确保项目ID正确设置
      if (!projectDetails.project.project_id && projectId) {
        console.log("[saveProject] Setting project_id from route param.");
        projectDetails.project.project_id = projectId;
      }

      // 提取各部分数据
      console.log("[saveProject] Extracting data from projectDetails.");
      const {
        project,
        sponsors = [],
        research_drugs = [],
        drug_groups = [],
        personnel = [],
        subsidy_schemes = [],
        subsidies = []
      } = projectDetails;

      // 转换数据格式 (Add logs if needed)
      console.log("[saveProject] Transforming sponsorsList.");
      const sponsorsList = sponsors.map((s: any) => ({
        id: s.id,
        project_id: s.project_id || projectId,
        sponsor_item_id: s.sponsor_item_id
      }));
      console.log("[saveProject] Transforming personnelList.");
      const personnelList = personnel.map((p: any) => ({
        assignment_id: p.assignment_id,
        project_id: p.project_id || projectId,
        personnel_id: p.personnel_id,
        role_item_id: p.role_item_id
      }));

      console.log("[saveProject] Raw data before processing subsidies:", { subsidy_schemes, subsidies });

      // --- 修改开始: 确保 total_amount 是浮点数 ---
      const ensureFloatAmount = (schemes: SubsidyScheme[] | undefined | null): SubsidyScheme[] => {
        if (!schemes) return [];
        return schemes.map(scheme => ({
          ...scheme,
          total_amount: parseFloat(String(scheme.total_amount || 0)) // 强制转为浮点数
        }));
      };
      const schemesWithFloatAmount = ensureFloatAmount(subsidy_schemes);
      console.log("[saveProject] Schemes with float amount:", schemesWithFloatAmount);
      // --- 修改结束 ---

      // 处理补贴方案中的included_subsidies
      console.log("[saveProject] Processing subsidy schemes...");
      const processedSubsidySchemes = schemesWithFloatAmount.map(scheme => {
        if (scheme.included_subsidies && scheme.included_subsidies.length > 0) {
          const validSubsidyIds = scheme.included_subsidies.filter((id: number) => 
            subsidies.some(subsidy => subsidy.subsidy_item_id === id)
          );
          return { ...scheme, included_subsidies: validSubsidyIds };
        }
        return scheme;
      });
      console.log("[saveProject] Processed subsidy schemes:", processedSubsidySchemes);

      // 清理数据
      let sanitizedProject;
      try {
        console.log("[saveProject] Calling sanitizeProjectData...");
        sanitizedProject = sanitizeProjectData({
          project,
          sponsors: sponsorsList,
          research_drugs,
          drug_groups,
          personnel: personnelList,
          subsidy_schemes: processedSubsidySchemes,
          subsidies
        });
        console.log("[saveProject] sanitizeProjectData finished.", sanitizedProject);
      } catch (sanitizeError) {
        console.error("[saveProject] Error during sanitizeProjectData:", sanitizeError);
        throw sanitizeError; // 重新抛出错误，以便外层catch捕获
      }

      console.log("[saveProject] Data sanitized. Final subsidy schemes:", JSON.stringify(sanitizedProject.subsidy_schemes));

      // 调用后端服务
      try {
        console.log("[saveProject] Calling projectManagementService.saveProjectWithDetails...");
        await projectManagementService.saveProjectWithDetails(
          sanitizedProject.project,
          sanitizedProject.sponsors,
          sanitizedProject.research_drugs,
          sanitizedProject.drug_groups,
          sanitizedProject.personnel,
          sanitizedProject.subsidy_schemes,
          sanitizedProject.subsidies
        );
        console.log("[saveProject] projectManagementService.saveProjectWithDetails finished successfully.");
      } catch (serviceError) {
        console.error("[saveProject] Error calling projectManagementService.saveProjectWithDetails:", serviceError);
        // 尝试更详细地打印错误，Tauri错误通常包含cause
        if (serviceError instanceof Error && serviceError.cause) {
            console.error("[saveProject] Service Error Cause:", serviceError.cause);
        }
        throw serviceError; // 重新抛出，让外层catch处理UI
      }

      // 显示成功消息
      console.log("[saveProject] Setting success message and navigating.");
      successMessage = "项目更新成功！";
      goto(`/projects/${projectId}`);

    } catch (err) {
      console.error("[saveProject] Error in main try block:", err);
      error = err instanceof Error ? err.message : String(err);
    } finally {
      console.log("[saveProject] Entering finally block.");
      isLoading = false;
      isSaving = false;

      // 5秒后自动清除成功消息
      if (successMessage) {
        setTimeout(() => {
          successMessage = null;
        }, 5000);
      }
      console.log("[saveProject] Function end.");
    }
  }

  // 保存草稿
  async function saveDraft() {
    await saveProject();
  }

  // 检查基本信息是否有效
  function isBasicInfoValid() {
    if (!projectDetails) return false;

    const { project } = projectDetails;
    return !!(
      project.project_name &&
      project.project_short_name &&
      project.disease_item_id
    );
  }

  // 切换调试模式
  function toggleDebug() {
    isDebug = !isDebug;
  }

  // 检查数据库表和数据
  async function checkDatabaseTables() {
    try {
      const result = await projectManagementService.checkDatabaseTables();
      console.log('数据库表和数据检查结果:', result);
      alert('数据库检查结果已输出到控制台');
    } catch (err) {
      console.error('检查数据库表和数据失败:', err);
      error = err instanceof Error ? err.message : String(err);
    }
  }

  // 重置数据库表
  async function resetDatabaseTables() {
    if (!confirm('警告：此操作将删除所有数据库表并重新创建它们。所有数据将丢失！确定要继续吗？')) {
      return;
    }

    try {
      const result = await projectManagementService.resetDatabaseTables();
      console.log('数据库表重置结果:', result);
      alert('数据库表已重置，请刷新页面');
      // 刷新页面
      window.location.reload();
    } catch (err) {
      console.error('重置数据库表失败:', err);
      error = err instanceof Error ? err.message : String(err);
    }
  }

  // 组件挂载时加载项目详情
  onMount(async () => {
    await loadProjectDetails();
  });
</script>

<div class="container mx-auto px-4 py-8">
  <div class="flex justify-between items-center mb-6">
    <div class="flex items-center gap-2">
      <Button variant="ghost" on:click={() => goto(`/projects/${projectId}`)}>
        <ArrowLeft class="h-4 w-4 mr-2" />
        返回详情
      </Button>
      <h1 class="text-2xl font-bold">
        编辑项目: {projectDetails?.project.project_short_name || ''}
      </h1>
      <Button variant="ghost" size="sm" on:click={toggleDebug}>
        {isDebug ? '关闭调试' : '调试'}
      </Button>
      {#if isDebug}
        <div class="flex gap-2">
          <Button variant="ghost" size="sm" on:click={checkDatabaseTables}>
            检查数据库
          </Button>
          <Button variant="ghost" size="sm" on:click={resetDatabaseTables} class="text-red-500">
            重置数据库
          </Button>
        </div>
      {/if}
    </div>
    <div class="flex gap-3">
      <Button variant="outline" on:click={saveDraft} disabled={isLoading || isSaving || !isBasicInfoValid()}>
        保存草稿
      </Button>
      <Button on:click={saveProject} disabled={isLoading || isSaving || !isBasicInfoValid()}>
        {#if isSaving}
          <div class="animate-spin h-4 w-4 mr-2 border-2 border-white rounded-full border-t-transparent"></div>
          保存中...
        {:else}
          <Save class="h-4 w-4 mr-2" />
          保存项目
        {/if}
      </Button>
    </div>
  </div>

  <!-- 错误提示 -->
  {#if error}
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
      <p>{error}</p>
    </div>
  {/if}

  <!-- 成功提示 -->
  {#if successMessage}
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
      <p>{successMessage}</p>
    </div>
  {/if}

  <!-- 调试信息 -->
  {#if isDebug && projectDetails}
    <div class="bg-gray-100 border border-gray-300 p-4 rounded mb-4 text-xs overflow-auto max-h-[200px]">
      <h3 class="font-bold mb-2">调试信息:</h3>
      <pre>{JSON.stringify(projectDetails, null, 2)}</pre>
    </div>
  {/if}

  <!-- 加载中 -->
  {#if isLoading}
    <div class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
    </div>
  {:else if projectDetails}
    <!-- 使用ProjectForm组件 -->
    <ProjectForm
      bind:projectDetails={projectDetails}
      onSave={saveProject}
      onSaveDraft={saveDraft}
      isLoading={isLoading || isSaving}
      isBasicInfoValid={isBasicInfoValid}
    />
  {:else}
    <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
      <p class="font-semibold">无法加载项目数据</p>
      <p>请返回项目列表并重试，或创建新项目。</p>
    </div>
  {/if}
</div>
