<script lang="ts">
  import { 
    validateDrugShare, 
    calculateDrugRatio, 
    getShareOptions,
    getSimplifiedRatioDisplay,
    calculatePercentageDistribution
  } from '$lib/utils/drugRatioUtils';

  // Test data
  let testDrugs = $state([
    { research_drug: 'Drug A', share: 2 },
    { research_drug: 'Drug B', share: 4 },
    { research_drug: 'Drug C', share: 2 }
  ]);

  let newDrugName = $state('');
  let newDrugShare = $state<number | null>(null);
  let validationError = $state('');

  // Reactive calculations
  let ratioDisplay = $derived(calculateDrugRatio(testDrugs));
  let simplifiedRatio = $derived(getSimplifiedRatioDisplay(testDrugs));
  let percentageDistribution = $derived(calculatePercentageDistribution(testDrugs));

  // Validation
  $effect(() => {
    validationError = validateDrugShare(newDrugShare);
  });

  function addDrug() {
    if (!newDrugName || !newDrugShare || validationError) return;
    
    testDrugs.push({
      research_drug: newDrugName,
      share: newDrugShare
    });
    
    // Reset form
    newDrugName = '';
    newDrugShare = null;
  }

  function removeDrug(index: number) {
    testDrugs.splice(index, 1);
  }
</script>

<div class="container mx-auto p-6 max-w-4xl">
  <h1 class="text-3xl font-bold mb-6">Drug Ratio System Test</h1>
  
  <!-- Add Drug Form -->
  <div class="bg-white border rounded-lg p-6 mb-6">
    <h2 class="text-xl font-semibold mb-4">Add Test Drug</h2>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <div>
        <label for="drugName" class="block text-sm font-medium mb-2">Drug Name</label>
        <input
          id="drugName"
          type="text"
          bind:value={newDrugName}
          placeholder="Enter drug name"
          class="w-full px-3 py-2 border border-gray-300 rounded-md"
        />
      </div>
      
      <div>
        <label for="drugShare" class="block text-sm font-medium mb-2">Share (1-9)</label>
        <select
          id="drugShare"
          bind:value={newDrugShare}
          class="w-full px-3 py-2 border border-gray-300 rounded-md"
          class:border-red-500={validationError}
        >
          <option value={null}>Select share</option>
          {#each getShareOptions() as option}
            <option value={option.value}>{option.label}</option>
          {/each}
        </select>
        {#if validationError}
          <p class="text-red-500 text-xs mt-1">{validationError}</p>
        {/if}
      </div>
      
      <div class="flex items-end">
        <button
          on:click={addDrug}
          disabled={!newDrugName || !newDrugShare || !!validationError}
          class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed"
        >
          Add Drug
        </button>
      </div>
    </div>
  </div>

  <!-- Current Drugs -->
  <div class="bg-white border rounded-lg p-6 mb-6">
    <h2 class="text-xl font-semibold mb-4">Current Drugs</h2>
    
    {#if testDrugs.length === 0}
      <p class="text-gray-500 text-center py-8">No drugs added yet</p>
    {:else}
      <div class="overflow-x-auto">
        <table class="w-full border-collapse">
          <thead>
            <tr class="bg-gray-50">
              <th class="text-left py-3 px-4 font-medium">Drug Name</th>
              <th class="text-left py-3 px-4 font-medium">Share</th>
              <th class="text-left py-3 px-4 font-medium">Percentage</th>
              <th class="text-right py-3 px-4 font-medium">Actions</th>
            </tr>
          </thead>
          <tbody>
            {#each testDrugs as drug, index}
              <tr class="border-t">
                <td class="py-3 px-4 font-medium">{drug.research_drug}</td>
                <td class="py-3 px-4">
                  <span class="inline-block bg-amber-50 text-amber-700 px-2 py-1 rounded-md text-xs font-medium">
                    {drug.share}
                  </span>
                </td>
                <td class="py-3 px-4">
                  {percentageDistribution.find(p => p.drug === drug)?.percentage || 0}%
                </td>
                <td class="text-right py-3 px-4">
                  <button
                    on:click={() => removeDrug(index)}
                    class="text-red-500 hover:text-red-700 text-sm"
                  >
                    Remove
                  </button>
                </td>
              </tr>
            {/each}
          </tbody>
        </table>
      </div>
    {/if}
  </div>

  <!-- Ratio Display -->
  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
      <h3 class="text-lg font-semibold text-blue-800 mb-2">Current Ratio</h3>
      <p class="text-2xl font-bold text-blue-900">
        {ratioDisplay || 'No drugs with shares'}
      </p>
      <p class="text-sm text-blue-700 mt-1">Raw ratio from shares</p>
    </div>
    
    <div class="bg-green-50 border border-green-200 rounded-lg p-6">
      <h3 class="text-lg font-semibold text-green-800 mb-2">Simplified Ratio</h3>
      <p class="text-2xl font-bold text-green-900">
        {simplifiedRatio || 'No drugs with shares'}
      </p>
      <p class="text-sm text-green-700 mt-1">Reduced to lowest terms</p>
    </div>
  </div>

  <!-- Example Scenarios -->
  <div class="mt-8 bg-gray-50 border rounded-lg p-6">
    <h3 class="text-lg font-semibold mb-4">Example Scenarios</h3>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
      <div class="bg-white p-4 rounded border">
        <h4 class="font-medium mb-2">Equal Distribution</h4>
        <p class="text-gray-600">3 drugs with share 1 each</p>
        <p class="font-mono text-blue-600">1:1:1 (33.33% each)</p>
      </div>
      
      <div class="bg-white p-4 rounded border">
        <h4 class="font-medium mb-2">Weighted Distribution</h4>
        <p class="text-gray-600">Drug A=2, Drug B=1</p>
        <p class="font-mono text-blue-600">2:1 (66.67% vs 33.33%)</p>
      </div>
      
      <div class="bg-white p-4 rounded border">
        <h4 class="font-medium mb-2">Complex Ratio</h4>
        <p class="text-gray-600">Drug A=3, Drug B=6, Drug C=3</p>
        <p class="font-mono text-blue-600">3:6:3 → 1:2:1</p>
      </div>
    </div>
  </div>
</div>
